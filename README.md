# Mobi - Mobile App with Animated Splash Screen 📱

This is an [Expo](https://expo.dev) React Native project featuring a beautiful animated splash screen with the "Mobi" branding.

## Features

✨ **Animated Splash Screen** with:
- Multi-step entrance animations using React Native Reanimated
- Custom logo with geometric design
- Smooth button animations with spring physics
- Beautiful gradient background with decorative elements
- Login and Register buttons with press animations
- "Skip for now" option to access main app

🎨 **Design Elements**:
- Blue gradient background (#1E3A8A to #3B82F6)
- Custom geometric logo with shadow effects
- Floating decorative circles
- Professional typography with text shadows
- Responsive button interactions

## Project Structure

```
app/
├── splash.tsx          # Main animated splash screen
├── login.tsx           # Login screen placeholder
├── register.tsx        # Register screen placeholder
├── index.tsx           # App entry point (redirects to splash)
├── _layout.tsx         # Root navigation layout
└── (tabs)/             # Main app tabs (existing)
```

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

3. Open the app in:
   - [Expo Go](https://expo.dev/go) on your mobile device
   - Web browser (press `w` in terminal)
   - [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/) (press `a`)
   - [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/) (press `i`)

## Animation Sequence

The splash screen features a carefully choreographed animation sequence:

1. **Background Scale** (0ms) - Background scales in with spring animation
2. **Logo Entrance** (300ms) - Logo scales and fades in
3. **Title Slide** (600ms) - "Mobi" title slides up and fades in
4. **Buttons Entrance** (900ms) - Buttons slide up and fade in
5. **Button Scaling** (1200ms+) - Individual button scale animations

## Technologies Used

- **Expo Router** - File-based navigation
- **React Native Reanimated** - High-performance animations
- **TypeScript** - Type safety
- **React Native** - Cross-platform mobile development

## Navigation Flow

```
Index → Splash Screen → Login/Register/Main App
```

- Splash screen is the entry point
- Login/Register buttons navigate to respective screens
- "Skip for now" button goes directly to main app tabs
- All screens have back navigation to splash

## Customization

The splash screen can be easily customized by modifying:
- Colors in the `styles` object
- Animation timing in `startAnimationSequence()`
- Logo design in the `logoIcon` components
- Text content and typography

## Learn more

To learn more about the technologies used:

- [Expo documentation](https://docs.expo.dev/)
- [React Native Reanimated](https://docs.swmansion.com/react-native-reanimated/)
- [Expo Router](https://docs.expo.dev/router/introduction/)
# mobi

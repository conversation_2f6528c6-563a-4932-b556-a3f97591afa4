/**
 * Modern color system for Mobi app - White & Blue Gradient Theme
 * Designed for clean, professional aesthetics with blue accents and white backgrounds
 * Following modern UX best practices for transport/mobility apps
 */

// Primary Colors - Blue Gradient System
const primaryBlue = '#2563EB';        // Main blue
const primaryBlueLight = '#3B82F6';   // Lighter blue
const primaryBlueDark = '#1D4ED8';    // Darker blue
const primaryBlueShade = '#60A5FA';   // Light shade for gradients

// Secondary Colors - Complementary Blues
const secondaryBlue = '#0EA5E9';      // Sky blue
const secondaryBlueLight = '#38BDF8'; // Light sky blue
const secondaryBlueDark = '#0284C7';  // Dark sky blue

// Gradient Colors
const gradientStart = '#FFFFFF';      // Pure white
const gradientMid = '#F8FAFF';        // Very light blue tint
const gradientEnd = '#EBF4FF';        // Light blue background

// Accent Colors
const warningColor = '#F59E0B';
const errorColor = '#EF4444';
const successColor = '#10B981';
const infoColor = primaryBlueLight;

// Neutral Palette
const gray = {
  50: '#F9FAFB',
  100: '#F3F4F6',
  200: '#E5E7EB',
  300: '#D1D5DB',
  400: '#9CA3AF',
  500: '#6B7280',
  600: '#4B5563',
  700: '#374151',
  800: '#1F2937',
  900: '#111827',
};

export const Colors = {
  light: {
    // Primary Colors - Blue System
    primary: primaryBlue,
    primaryLight: primaryBlueLight,
    primaryDark: primaryBlueDark,
    primaryShade: primaryBlueShade,

    // Secondary Colors - Sky Blue System
    secondary: secondaryBlue,
    secondaryLight: secondaryBlueLight,
    secondaryDark: secondaryBlueDark,

    // Gradient Colors
    gradientStart: gradientStart,
    gradientMid: gradientMid,
    gradientEnd: gradientEnd,

    // Accent Colors
    warning: warningColor,
    error: errorColor,
    info: infoColor,
    success: successColor,

    // Text Colors - Optimized for white/blue theme
    text: gray[900],
    textSecondary: gray[700],
    textTertiary: gray[600],
    textMuted: gray[500],
    textOnBlue: '#FFFFFF',
    textBlue: primaryBlue,

    // Background Colors - White & Blue Gradient System
    background: gradientStart,
    backgroundSecondary: gradientMid,
    backgroundTertiary: gradientEnd,
    backgroundBlue: primaryBlue,
    backgroundBlueLight: primaryBlueShade,

    // Surface Colors - Clean white surfaces
    surface: '#FFFFFF',
    surfaceSecondary: gradientMid,
    surfaceBlue: primaryBlueLight,

    // Border Colors - Subtle blue tints
    border: gray[200],
    borderLight: '#E1E9FF',
    borderBlue: primaryBlueShade,

    // Legacy support
    tint: primaryBlue,
    icon: gray[600],
    tabIconDefault: gray[500],
    tabIconSelected: primaryBlue,
  },
  dark: {
    // Primary Colors - Blue System (Dark Mode)
    primary: primaryBlueShade,
    primaryLight: '#93C5FD',
    primaryDark: primaryBlue,
    primaryShade: '#DBEAFE',

    // Secondary Colors - Sky Blue System (Dark Mode)
    secondary: secondaryBlueLight,
    secondaryLight: '#7DD3FC',
    secondaryDark: secondaryBlue,

    // Gradient Colors (Dark Mode)
    gradientStart: gray[900],
    gradientMid: gray[800],
    gradientEnd: '#1E3A8A',

    // Accent Colors
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',
    success: '#34D399',

    // Text Colors - Optimized for dark theme
    text: gray[50],
    textSecondary: gray[200],
    textTertiary: gray[300],
    textMuted: gray[400],
    textOnBlue: '#FFFFFF',
    textBlue: primaryBlueShade,

    // Background Colors - Dark with blue accents
    background: gray[900],
    backgroundSecondary: gray[800],
    backgroundTertiary: gray[700],
    backgroundBlue: primaryBlueDark,
    backgroundBlueLight: '#1E40AF',

    // Surface Colors - Dark surfaces
    surface: gray[800],
    surfaceSecondary: gray[700],
    surfaceBlue: '#1E40AF',

    // Border Colors - Dark with blue tints
    border: gray[600],
    borderLight: gray[700],
    borderBlue: '#3B82F6',

    // Legacy support
    tint: primaryBlueShade,
    icon: gray[400],
    tabIconDefault: gray[500],
    tabIconSelected: primaryBlueShade,
  },
};

import * as Location from 'expo-location';
import { useEffect, useState } from 'react';
import { Alert, Platform } from 'react-native';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp: number;
}

interface LocationTrackingState {
  currentLocation: LocationData | null;
  isTracking: boolean;
  hasPermission: boolean;
  error: string | null;
  locationHistory: LocationData[];
  subscription?: Location.LocationSubscription;
}

export const useLocationTracking = () => {
  const [state, setState] = useState<LocationTrackingState>({
    currentLocation: null,
    isTracking: false,
    hasPermission: false,
    error: null,
    locationHistory: [],
  });

  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      // Check if location services are enabled
      const servicesEnabled = await Location.hasServicesEnabledAsync();
      if (!servicesEnabled) {
        Alert.alert(
          'خدمات الموقع غير مفعلة',
          'يرجى تفعيل خدمات الموقع في إعدادات الجهاز',
          [{ text: 'موافق' }]
        );
        return false;
      }

      // Request foreground permissions
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      
      if (foregroundStatus !== 'granted') {
        Alert.alert(
          'إذن الموقع مطلوب',
          'يحتاج التطبيق إلى إذن الوصول للموقع لتتبع الرحلة',
          [{ text: 'موافق' }]
        );
        setState(prev => ({ ...prev, error: 'Location permission denied' }));
        return false;
      }

      // Request background permissions for continuous tracking
      if (Platform.OS === 'android') {
        const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
        if (backgroundStatus !== 'granted') {
          Alert.alert(
            'إذن الموقع في الخلفية',
            'للحصول على أفضل تجربة تتبع، يرجى السماح بالوصول للموقع في الخلفية',
            [{ text: 'موافق' }]
          );
        }
      }

      setState(prev => ({ ...prev, hasPermission: true, error: null }));
      return true;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to request location permission',
        hasPermission: false 
      }));
      return false;
    }
  };

  const getCurrentLocation = async (): Promise<LocationData | null> => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: 10000, // 10 seconds
      });

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || undefined,
        timestamp: location.timestamp,
      };

      setState(prev => ({ 
        ...prev, 
        currentLocation: locationData,
        error: null 
      }));

      return locationData;
    } catch (error) {
      console.error('Error getting current location:', error);
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to get current location' 
      }));
      return null;
    }
  };

  const startTracking = async (): Promise<boolean> => {
    if (!state.hasPermission) {
      const hasPermission = await requestLocationPermission();
      if (!hasPermission) return false;
    }

    try {
      setState(prev => ({ ...prev, isTracking: true, error: null }));

      // Start location updates
      const subscription = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000, // Update every 5 seconds
          distanceInterval: 10, // Update every 10 meters
        },
        (location) => {
          const locationData: LocationData = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy || undefined,
            timestamp: location.timestamp,
          };

          setState(prev => ({
            ...prev,
            currentLocation: locationData,
            locationHistory: [...prev.locationHistory, locationData].slice(-50), // Keep last 50 locations
          }));
        }
      );

      // Store subscription for cleanup
      setState(prev => ({ ...prev, subscription }));
      return true;
    } catch (error) {
      console.error('Error starting location tracking:', error);
      setState(prev => ({ 
        ...prev, 
        isTracking: false,
        error: 'Failed to start location tracking' 
      }));
      return false;
    }
  };

  const stopTracking = () => {
    setState(prev => {
      if (prev.subscription) {
        prev.subscription.remove();
      }
      return {
        ...prev,
        isTracking: false,
        subscription: undefined,
      };
    });
  };

  // Initialize permissions on mount
  useEffect(() => {
    requestLocationPermission();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (state.subscription) {
        state.subscription.remove();
      }
    };
  }, [state.subscription]);

  return {
    ...state,
    requestLocationPermission,
    getCurrentLocation,
    startTracking,
    stopTracking,
  };
};

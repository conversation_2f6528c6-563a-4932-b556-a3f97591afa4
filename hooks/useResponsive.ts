import { useEffect, useState } from 'react';
import { Dimensions } from 'react-native';

interface ScreenDimensions {
  width: number;
  height: number;
}

interface ResponsiveValues {
  isSmallScreen: boolean;
  isMediumScreen: boolean;
  isLargeScreen: boolean;
  screenWidth: number;
  screenHeight: number;
  isPortrait: boolean;
  isLandscape: boolean;
}

const BREAKPOINTS = {
  small: 375,
  medium: 768,
  large: 1024,
};

export const useResponsive = (): ResponsiveValues => {
  const [dimensions, setDimensions] = useState<ScreenDimensions>(() => {
    const { width, height } = Dimensions.get('window');
    return { width, height };
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions({ width: window.width, height: window.height });
    });

    return () => subscription?.remove();
  }, []);

  const { width, height } = dimensions;

  return {
    isSmallScreen: width < BREAKPOINTS.small,
    isMediumScreen: width >= BREAKPOINTS.small && width < BREAKPOINTS.medium,
    isLargeScreen: width >= BREAKPOINTS.medium,
    screenWidth: width,
    screenHeight: height,
    isPortrait: height > width,
    isLandscape: width > height,
  };
};

// Utility function to get responsive values
export const getResponsiveValue = <T>(
  values: {
    small?: T;
    medium?: T;
    large?: T;
    default: T;
  },
  screenWidth: number
): T => {
  if (screenWidth < BREAKPOINTS.small && values.small !== undefined) {
    return values.small;
  }
  if (screenWidth >= BREAKPOINTS.small && screenWidth < BREAKPOINTS.medium && values.medium !== undefined) {
    return values.medium;
  }
  if (screenWidth >= BREAKPOINTS.medium && values.large !== undefined) {
    return values.large;
  }
  return values.default;
};

// Utility function to scale values based on screen size
export const scaleSize = (size: number, screenWidth: number): number => {
  const baseWidth = 375; // iPhone X width as base
  const scale = screenWidth / baseWidth;
  return Math.round(size * scale);
};

// Utility function to get responsive padding/margin
export const getResponsiveSpacing = (
  baseSpacing: number,
  screenWidth: number
): number => {
  return getResponsiveValue(
    {
      small: baseSpacing * 0.8,
      medium: baseSpacing,
      large: baseSpacing * 1.2,
      default: baseSpacing,
    },
    screenWidth
  );
};

// Utility function to get responsive font size
export const getResponsiveFontSize = (
  baseFontSize: number,
  screenWidth: number
): number => {
  return getResponsiveValue(
    {
      small: baseFontSize * 0.9,
      medium: baseFontSize,
      large: baseFontSize * 1.1,
      default: baseFontSize,
    },
    screenWidth
  );
};

import GoogleMapsRoute from '@/components/GoogleMapsRoute';
import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Dimensions,
    FlatList,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

const { width, height } = Dimensions.get('window');

// Trip trajectory data from Trip Info page
const TRIP_LOCATIONS = [
  {
    id: 1,
    name: 'منزل الطالب',
    type: 'home',
    latitude: 34.0209,
    longitude: -6.8416,
  },
  {
    id: 2,
    name: 'محطة الحافلة',
    type: 'bus_stop',
    latitude: 34.0250,
    longitude: -6.8350,
  },
  {
    id: 3,
    name: 'الجامعة',
    type: 'university',
    latitude: 34.0300,
    longitude: -6.8200,
  },
];

// Student data with statuses
const STUDENTS = [
  {
    id: 1,
    name: 'أحمد محمد',
    avatar: 'أ',
    status: 'waiting',
    statusText: 'في الانتظار',
    statusColor: '#f59e0b',
    location: 'محطة الحافلة الرئيسية',
    estimatedTime: '5 دقائق',
  },
  {
    id: 2,
    name: 'فاطمة الزهراء',
    avatar: 'ف',
    status: 'onboard',
    statusText: 'في الحافلة',
    statusColor: '#10b981',
    location: 'في الطريق',
    estimatedTime: '15 دقيقة',
  },
  {
    id: 3,
    name: 'عمر الخطاب',
    avatar: 'ع',
    status: 'late',
    statusText: 'متأخر',
    statusColor: '#ef4444',
    location: 'لم يصل بعد',
    estimatedTime: '10 دقائق',
  },
  {
    id: 4,
    name: 'سارة أحمد',
    avatar: 'س',
    status: 'waiting',
    statusText: 'في الانتظار',
    statusColor: '#f59e0b',
    location: 'محطة الجامعة',
    estimatedTime: '3 دقائق',
  },
  {
    id: 5,
    name: 'محمد علي',
    avatar: 'م',
    status: 'onboard',
    statusText: 'في الحافلة',
    statusColor: '#10b981',
    location: 'في الطريق',
    estimatedTime: '12 دقيقة',
  },
];

// Student login info
const STUDENT_LOGIN = {
  name: 'أحمد محمد',
  studentId: 'ST-2024-001',
  university: 'جامعة محمد الخامس',
};

export default function LiveStudentTrackingScreen() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(1800); // 30 minutes in seconds

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 0) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleGoPress = () => {
    router.push('/live-tracking');
  };

  const renderStudent = ({ item }: { item: any }) => (
    <View style={styles.studentCard}>
      <View style={styles.studentInfo}>
        <View style={[styles.studentAvatar, { backgroundColor: item.statusColor }]}>
          <Text style={styles.studentAvatarText}>{item.avatar}</Text>
        </View>
        <View style={styles.studentDetails}>
          <Text style={styles.studentName}>{item.name}</Text>
          <Text style={styles.studentLocation}>{item.location}</Text>
        </View>
      </View>
      <View style={styles.studentStatus}>
        <View style={[styles.statusDot, { backgroundColor: item.statusColor }]} />
        <Text style={[styles.statusText, { color: item.statusColor }]}>{item.statusText}</Text>
        <Text style={styles.estimatedTime}>{item.estimatedTime}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="white" translucent={false} />

      {/* Student Login Info with Countdown Timer */}
      <View style={styles.topSection}>
        <View style={styles.loginCard}>
          <View style={styles.loginInfo}>
            <View style={styles.loginAvatar}>
              <Text style={styles.loginAvatarText}>{STUDENT_LOGIN.name.charAt(0)}</Text>
            </View>
            <View style={styles.loginDetails}>
              <Text style={styles.loginName}>{STUDENT_LOGIN.name}</Text>
              <Text style={styles.loginId}>{STUDENT_LOGIN.studentId}</Text>
              <Text style={styles.loginUniversity}>{STUDENT_LOGIN.university}</Text>
            </View>
          </View>
          <View style={styles.timerSection}>
            <Text style={styles.timerLabel}>الوقت المتبقي</Text>
            <Text style={styles.timerValue}>{formatTime(countdown)}</Text>
          </View>
        </View>
      </View>

      {/* Main Content: Map and Students List */}
      <View style={styles.mainContent}>
        {/* Trip Trajectory Map */}
        <View style={styles.mapSection}>
          <Text style={styles.sectionTitle}>خريطة مواقع الطلاب المباشرة</Text>
          <View style={styles.mapContainer}>
            <GoogleMapsRoute
              locations={TRIP_LOCATIONS}
              style={styles.map}
              routeData={{
                duration: '40 min',
                distance: '12.5 km',
                eta: '08:30',
              }}
            />
            <View style={styles.mapTitleOverlay}>
              <Text style={styles.mapTitleText}>60 كم • المدة المتوقعة: 30:00</Text>
            </View>
          </View>
        </View>

        {/* Students List Below Map */}
        <View style={styles.studentsSection}>
          <View style={styles.studentsHeader}>
            <Text style={styles.sectionTitle}>قائمة الطلاب</Text>
            <Text style={styles.studentsCount}>{STUDENTS.length} طلاب</Text>
          </View>
          <FlatList
            data={STUDENTS}
            renderItem={renderStudent}
            keyExtractor={(item) => item.id.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.studentsList}
            style={styles.studentsListContainer}
          />
        </View>
      </View>

      {/* GO Button */}
      <View style={styles.goButtonContainer}>
        <TouchableOpacity
          style={styles.goButton}
          onPress={handleGoPress}
          activeOpacity={0.8}
        >
          <Text style={styles.goButtonIcon}>🚌</Text>
          <Text style={styles.goButtonText}>معلومات المركبة</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },

  topSection: {
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 24,
    backgroundColor: '#ffffff',
  },
  mainContent: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  loginCard: {
    backgroundColor: '#f8fafc',
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  loginInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  loginAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  loginAvatarText: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  loginDetails: {
    flex: 1,
  },
  loginName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'right',
  },
  loginId: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginTop: 2,
    textAlign: 'right',
  },
  loginUniversity: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    marginTop: 2,
    textAlign: 'right',
  },
  timerSection: {
    alignItems: 'center',
  },
  timerLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  timerValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.primary,
  },
  mapSection: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    backgroundColor: '#f8fafc',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 12,
    textAlign: 'right',
  },
  mapContainer: {
    height: 240,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  mapTitleOverlay: {
    position: 'absolute',
    top: 12,
    left: 12,
    right: 12,
    zIndex: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  mapTitleText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748b',
    textAlign: 'center',
  },
  map: {
    flex: 1,
  },
  studentsSection: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 8,
    backgroundColor: '#ffffff',
  },
  studentsListContainer: {
    flex: 1,
  },
  studentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 0,
  },
  studentsCount: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
  studentsList: {
    paddingBottom: 100,
  },
  studentCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  studentInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  studentAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  studentAvatarText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    textAlign: 'right',
    marginBottom: 2,
  },
  studentLocation: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'right',
  },
  studentStatus: {
    alignItems: 'center',
    minWidth: 70,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
    textAlign: 'center',
  },
  estimatedTime: {
    fontSize: 11,
    color: '#64748b',
    textAlign: 'center',
  },
  goButtonContainer: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
  },
  goButton: {
    height: 50,
    borderRadius: 12,
    backgroundColor: '#3b82f6',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  goButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginLeft: 8,
  },
  goButtonIcon: {
    fontSize: 18,
    color: 'white',
  },
});

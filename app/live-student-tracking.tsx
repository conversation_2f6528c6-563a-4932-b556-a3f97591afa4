import GoogleMapsRoute from '@/components/GoogleMapsRoute';
import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Dimensions,
    FlatList,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

const { width, height } = Dimensions.get('window');

// Trip trajectory data from Trip Info page
const TRIP_LOCATIONS = [
  {
    id: 1,
    name: 'منزل الطالب',
    type: 'home',
    latitude: 34.0209,
    longitude: -6.8416,
  },
  {
    id: 2,
    name: 'محطة الحافلة',
    type: 'bus_stop',
    latitude: 34.0250,
    longitude: -6.8350,
  },
  {
    id: 3,
    name: 'الجامعة',
    type: 'university',
    latitude: 34.0300,
    longitude: -6.8200,
  },
];

// Student data with statuses
const STUDENTS = [
  {
    id: 1,
    name: 'أحمد محمد',
    avatar: 'أ',
    status: 'waiting',
    statusText: 'في الانتظار',
    statusColor: '#f59e0b',
    location: 'محطة الحافلة الرئيسية',
    estimatedTime: '5 دقائق',
  },
  {
    id: 2,
    name: 'فاطمة الزهراء',
    avatar: 'ف',
    status: 'onboard',
    statusText: 'في الحافلة',
    statusColor: '#10b981',
    location: 'في الطريق',
    estimatedTime: '15 دقيقة',
  },
  {
    id: 3,
    name: 'عمر الخطاب',
    avatar: 'ع',
    status: 'late',
    statusText: 'متأخر',
    statusColor: '#ef4444',
    location: 'لم يصل بعد',
    estimatedTime: '10 دقائق',
  },
  {
    id: 4,
    name: 'سارة أحمد',
    avatar: 'س',
    status: 'waiting',
    statusText: 'في الانتظار',
    statusColor: '#f59e0b',
    location: 'محطة الجامعة',
    estimatedTime: '3 دقائق',
  },
  {
    id: 5,
    name: 'محمد علي',
    avatar: 'م',
    status: 'onboard',
    statusText: 'في الحافلة',
    statusColor: '#10b981',
    location: 'في الطريق',
    estimatedTime: '12 دقيقة',
  },
];

// Student login info
const STUDENT_LOGIN = {
  name: 'أحمد محمد',
  studentId: 'ST-2024-001',
  university: 'جامعة محمد الخامس',
};

export default function LiveStudentTrackingScreen() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(1800); // 30 minutes in seconds

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 0) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleGoPress = () => {
    router.push('/live-tracking');
  };

  const renderStudent = ({ item }: { item: any }) => (
    <View style={styles.studentCard}>
      <View style={styles.studentInfo}>
        <View style={[styles.studentAvatar, { backgroundColor: item.statusColor }]}>
          <Text style={styles.studentAvatarText}>{item.avatar}</Text>
        </View>
        <View style={styles.studentDetails}>
          <Text style={styles.studentName}>{item.name}</Text>
          <Text style={styles.studentLocation}>{item.location}</Text>
        </View>
      </View>
      <View style={styles.studentStatus}>
        <View style={[styles.statusDot, { backgroundColor: item.statusColor }]} />
        <Text style={[styles.statusText, { color: item.statusColor }]}>{item.statusText}</Text>
        <Text style={styles.estimatedTime}>{item.estimatedTime}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Header with Student Login and Countdown Timer */}
      <View style={styles.header}>
        <View style={styles.headerBackground} />
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>تتبع الطلاب المباشر</Text>
          <Text style={styles.headerSubtitle}>Live Student Tracking</Text>
        </View>
      </View>

      {/* Student Login Info with Countdown Timer */}
      <View style={styles.topSection}>
        <View style={styles.loginCard}>
          <View style={styles.loginInfo}>
            <View style={styles.loginAvatar}>
              <Text style={styles.loginAvatarText}>{STUDENT_LOGIN.name.charAt(0)}</Text>
            </View>
            <View style={styles.loginDetails}>
              <Text style={styles.loginName}>{STUDENT_LOGIN.name}</Text>
              <Text style={styles.loginId}>{STUDENT_LOGIN.studentId}</Text>
              <Text style={styles.loginUniversity}>{STUDENT_LOGIN.university}</Text>
            </View>
          </View>
          <View style={styles.timerSection}>
            <Text style={styles.timerLabel}>الوقت المتبقي</Text>
            <Text style={styles.timerValue}>{formatTime(countdown)}</Text>
          </View>
        </View>
      </View>

      {/* Main Content: Map and Students List */}
      <View style={styles.mainContent}>
        {/* Trip Trajectory Map */}
        <View style={styles.mapSection}>
          <Text style={styles.sectionTitle}>خريطة مواقع الطلاب المباشرة</Text>
          <View style={styles.mapContainer}>
            <GoogleMapsRoute
              locations={TRIP_LOCATIONS}
              style={styles.map}
              routeData={{
                duration: '40 min',
                distance: '12.5 km',
                eta: '08:30',
              }}
            />
            <View style={styles.mapTitleOverlay}>
              <Text style={styles.mapTitleText}>60 كم • المدة المتوقعة: 30:00</Text>
            </View>
          </View>
        </View>

        {/* Students List Below Map */}
        <View style={styles.studentsSection}>
          <View style={styles.studentsHeader}>
            <Text style={styles.sectionTitle}>قائمة الطلاب</Text>
            <Text style={styles.studentsCount}>{STUDENTS.length} طلاب</Text>
          </View>
          <FlatList
            data={STUDENTS}
            renderItem={renderStudent}
            keyExtractor={(item) => item.id.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.studentsList}
            style={styles.studentsListContainer}
          />
        </View>
      </View>

      {/* GO Button */}
      <View style={styles.goButtonContainer}>
        <TouchableOpacity
          style={styles.goButton}
          onPress={handleGoPress}
          activeOpacity={0.8}
        >
          <Text style={styles.goButtonIcon}>🚌</Text>
          <Text style={styles.goButtonText}>معلومات المركبة</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 60 : 45,
    paddingBottom: 24,
    paddingHorizontal: 24,
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.primary,
    opacity: 0.95,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    alignItems: 'center',
    justifyContent: 'center',
    backdropFilter: 'blur(10px)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  backIcon: {
    fontSize: 18,
    color: 'white',
    fontWeight: '700',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '800',
    color: 'white',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 13,
    color: 'rgba(255, 255, 255, 0.85)',
    marginTop: 4,
    fontWeight: '500',
    letterSpacing: 0.3,
  },
  topSection: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(59, 130, 246, 0.1)',
  },
  mainContent: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingTop: Platform.OS === 'ios' ? 120 : 100,
  },
  loginCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 24,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.15)',
    backdropFilter: 'blur(10px)',
  },
  loginInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  loginAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  loginAvatarText: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  loginDetails: {
    flex: 1,
  },
  loginName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'right',
  },
  loginId: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginTop: 2,
    textAlign: 'right',
  },
  loginUniversity: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    marginTop: 2,
    textAlign: 'right',
  },
  timerSection: {
    alignItems: 'center',
  },
  timerLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  timerValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.primary,
  },
  mapSection: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 16,
    textAlign: 'right',
    letterSpacing: 0.3,
  },
  mapContainer: {
    height: 280,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 8,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
  mapTitleOverlay: {
    position: 'absolute',
    top: 16,
    left: 16,
    right: 16,
    zIndex: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backdropFilter: 'blur(10px)',
  },
  mapTitleText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'center',
  },
  map: {
    flex: 1,
  },
  studentsSection: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 12,
  },
  studentsListContainer: {
    flex: 1,
  },
  studentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  studentsCount: {
    fontSize: 15,
    color: Colors.light.primary,
    fontWeight: '700',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  studentsList: {
    paddingBottom: 120,
  },
  studentCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.08)',
    transform: [{ scale: 1 }],
  },
  studentInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  studentAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  studentAvatarText: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    letterSpacing: 0.5,
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    textAlign: 'right',
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  studentLocation: {
    fontSize: 15,
    color: Colors.light.textSecondary,
    textAlign: 'right',
    fontWeight: '500',
  },
  studentStatus: {
    alignItems: 'center',
    minWidth: 80,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginBottom: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  statusText: {
    fontSize: 13,
    fontWeight: '700',
    marginBottom: 4,
    textAlign: 'center',
  },
  estimatedTime: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    fontWeight: '600',
    backgroundColor: 'rgba(107, 114, 128, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    textAlign: 'center',
  },
  goButtonContainer: {
    position: 'absolute',
    bottom: 40,
    left: 24,
    right: 24,
  },
  goButton: {
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 12,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    flexDirection: 'row',
  },
  goButtonText: {
    fontSize: 18,
    fontWeight: '800',
    color: 'white',
    letterSpacing: 1.5,
    marginLeft: 8,
  },
  goButtonIcon: {
    fontSize: 20,
    color: 'white',
    fontWeight: '700',
  },
});

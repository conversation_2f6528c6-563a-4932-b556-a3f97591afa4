import GoogleMapsRoute from '@/components/GoogleMapsRoute';
import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Dimensions,
    FlatList,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

const { width, height } = Dimensions.get('window');

// Trip trajectory data from Trip Info page
const TRIP_LOCATIONS = [
  {
    id: 1,
    name: 'منزل الطالب',
    type: 'home',
    latitude: 34.0209,
    longitude: -6.8416,
  },
  {
    id: 2,
    name: 'محطة الحافلة',
    type: 'bus_stop',
    latitude: 34.0250,
    longitude: -6.8350,
  },
  {
    id: 3,
    name: 'الجامعة',
    type: 'university',
    latitude: 34.0300,
    longitude: -6.8200,
  },
];

// Student data with statuses
const STUDENTS = [
  {
    id: 1,
    name: 'أحمد محمد',
    avatar: 'أ',
    status: 'waiting',
    statusText: 'في الانتظار',
    statusColor: '#f59e0b',
    location: 'محطة الحافلة الرئيسية',
    estimatedTime: '5 دقائق',
  },
  {
    id: 2,
    name: 'فاطمة الزهراء',
    avatar: 'ف',
    status: 'onboard',
    statusText: 'في الحافلة',
    statusColor: '#10b981',
    location: 'في الطريق',
    estimatedTime: '15 دقيقة',
  },
  {
    id: 3,
    name: 'عمر الخطاب',
    avatar: 'ع',
    status: 'late',
    statusText: 'متأخر',
    statusColor: '#ef4444',
    location: 'لم يصل بعد',
    estimatedTime: '10 دقائق',
  },
  {
    id: 4,
    name: 'سارة أحمد',
    avatar: 'س',
    status: 'waiting',
    statusText: 'في الانتظار',
    statusColor: '#f59e0b',
    location: 'محطة الجامعة',
    estimatedTime: '3 دقائق',
  },
  {
    id: 5,
    name: 'محمد علي',
    avatar: 'م',
    status: 'onboard',
    statusText: 'في الحافلة',
    statusColor: '#10b981',
    location: 'في الطريق',
    estimatedTime: '12 دقيقة',
  },
];

// Student login info
const STUDENT_LOGIN = {
  name: 'أحمد محمد',
  studentId: 'ST-2024-001',
  university: 'جامعة محمد الخامس',
};

export default function LiveStudentTrackingScreen() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(1800); // 30 minutes in seconds

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 0) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleGoPress = () => {
    router.push('/live-tracking');
  };

  const renderStudent = ({ item }: { item: any }) => (
    <View style={styles.studentCard}>
      <View style={styles.studentInfo}>
        <View style={[styles.studentAvatar, { backgroundColor: item.statusColor }]}>
          <Text style={styles.studentAvatarText}>{item.avatar}</Text>
        </View>
        <View style={styles.studentDetails}>
          <Text style={styles.studentName}>{item.name}</Text>
          <Text style={styles.studentLocation}>{item.location}</Text>
        </View>
      </View>
      <View style={styles.studentStatus}>
        <View style={[styles.statusDot, { backgroundColor: item.statusColor }]} />
        <Text style={[styles.statusText, { color: item.statusColor }]}>{item.statusText}</Text>
        <Text style={styles.estimatedTime}>{item.estimatedTime}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Header with Student Login and Countdown Timer */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>تتبع الطلاب المباشر</Text>
          <Text style={styles.headerSubtitle}>Live Student Tracking</Text>
        </View>
      </View>

      {/* Student Login Info with Countdown Timer */}
      <View style={styles.topSection}>
        <View style={styles.loginCard}>
          <View style={styles.loginInfo}>
            <View style={styles.loginAvatar}>
              <Text style={styles.loginAvatarText}>{STUDENT_LOGIN.name.charAt(0)}</Text>
            </View>
            <View style={styles.loginDetails}>
              <Text style={styles.loginName}>{STUDENT_LOGIN.name}</Text>
              <Text style={styles.loginId}>{STUDENT_LOGIN.studentId}</Text>
              <Text style={styles.loginUniversity}>{STUDENT_LOGIN.university}</Text>
            </View>
          </View>
          <View style={styles.timerSection}>
            <Text style={styles.timerLabel}>الوقت المتبقي</Text>
            <Text style={styles.timerValue}>{formatTime(countdown)}</Text>
          </View>
        </View>
      </View>

      {/* Main Content: Map and Students List */}
      <View style={styles.mainContent}>
        {/* Trip Trajectory Map */}
        <View style={styles.mapSection}>
          <View style={styles.mapContainer}>
            <GoogleMapsRoute
              locations={TRIP_LOCATIONS}
              style={styles.map}
              routeData={{
                duration: '40 min',
                distance: '12.5 km',
                eta: '08:30',
              }}
            />
          </View>
        </View>

        {/* Students List Below Map */}
        <View style={styles.studentsSection}>
          <View style={styles.studentsHeader}>
            <Text style={styles.sectionTitle}>قائمة الطلاب</Text>
            <Text style={styles.studentsCount}>{STUDENTS.length} طلاب</Text>
          </View>
          <FlatList
            data={STUDENTS}
            renderItem={renderStudent}
            keyExtractor={(item) => item.id.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.studentsList}
            style={styles.studentsListContainer}
          />
        </View>
      </View>

      {/* GO Button */}
      <View style={styles.goButtonContainer}>
        <TouchableOpacity
          style={styles.goButton}
          onPress={handleGoPress}
          activeOpacity={0.8}
        >
          <Text style={styles.goButtonText}>GO</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 20,
    backgroundColor: Colors.light.primary,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backIcon: {
    fontSize: 20,
    color: 'white',
    fontWeight: '600',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  topSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loginCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  loginInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  loginAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  loginAvatarText: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  loginDetails: {
    flex: 1,
  },
  loginName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'right',
  },
  loginId: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginTop: 2,
    textAlign: 'right',
  },
  loginUniversity: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    marginTop: 2,
    textAlign: 'right',
  },
  timerSection: {
    alignItems: 'center',
  },
  timerLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  timerValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.primary,
  },
  mapSection: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
    textAlign: 'right',
  },
  mapContainer: {
    height: 240,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    backgroundColor: 'white',
  },
  map: {
    flex: 1,
  },
  studentsSection: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 8,
  },
  studentsListContainer: {
    flex: 1,
  },
  studentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  studentsCount: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  studentsList: {
    paddingBottom: 100,
  },
  studentCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 18,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.04)',
  },
  studentInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  studentAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  studentAvatarText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'right',
  },
  studentLocation: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginTop: 2,
    textAlign: 'right',
  },
  studentStatus: {
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  estimatedTime: {
    fontSize: 11,
    color: Colors.light.textTertiary,
  },
  goButtonContainer: {
    position: 'absolute',
    bottom: 30,
    right: 30,
  },
  goButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.35,
    shadowRadius: 12,
    elevation: 10,
    borderWidth: 3,
    borderColor: 'white',
  },
  goButtonText: {
    fontSize: 20,
    fontWeight: '800',
    color: 'white',
    letterSpacing: 1,
  },
});

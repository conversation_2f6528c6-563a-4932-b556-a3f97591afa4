import DriverNavigationBar from '@/components/DriverNavigationBar';
import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Dimensions,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import MapView, { <PERSON><PERSON>, Polyline, PROVIDER_GOOGLE } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

// Trip data matching your description
const TRIP_DATA = {
  title: 'معلومات الرحلة',
  startTime: '7:00',
  endTime: '8:30',
  destination: 'جامعة العلوم',
  studentCount: 23,
  route: [
    { 
      latitude: 34.0209, 
      longitude: -6.8416,
      title: 'نقطة البداية',
      time: '7:00'
    },
    { 
      latitude: 34.0300, 
      longitude: -6.8200,
      title: 'جامعة العلوم',
      time: '8:30'
    },
  ],
};

export default function DriverTripTrackingScreen() {
  const router = useRouter();
  const [isStarting, setIsStarting] = useState(false);
  const [tripStarted, setTripStarted] = useState(false);

  const handleStartTrip = () => {
    setIsStarting(true);

    setTimeout(() => {
      setIsStarting(false);
      Alert.alert(
        'تم بدء الرحلة',
        'تم بدء الرحلة بنجاح. سيتم إشعار الطلاب بموقعك.',
        [
          {
            text: 'موافق',
            onPress: () => router.push('/driver-live-trip'),
          }
        ]
      );
    }, 2000);
  };

  const handleEndTrip = () => {
    Alert.alert(
      'إنهاء الرحلة',
      'هل أنت متأكد من إنهاء الرحلة؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'إنهاء',
          style: 'destructive',
          onPress: () => {
            setTripStarted(false);
            Alert.alert('تم إنهاء الرحلة', 'تم إنهاء الرحلة بنجاح.');
          }
        }
      ]
    );
  };

  const mapRegion = {
    latitude: 34.0255,
    longitude: -6.8300,
    latitudeDelta: 0.02,
    longitudeDelta: 0.02,
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Full Screen Map */}
      <MapView
        style={styles.fullScreenMap}
        provider={PROVIDER_GOOGLE}
        initialRegion={mapRegion}
        showsUserLocation={true}
        showsMyLocationButton={false}
        showsCompass={false}
        toolbarEnabled={false}
      >
        {/* Route Polyline */}
        <Polyline
          coordinates={TRIP_DATA.route}
          strokeColor={Colors.light.primary}
          strokeWidth={4}
          lineDashPattern={[5, 5]}
        />

        {/* Route Markers */}
        {TRIP_DATA.route.map((location, index) => (
          <Marker
            key={index}
            coordinate={location}
            pinColor={index === 0 ? Colors.light.success : Colors.light.error}
            title={location.title}
            description={`الوقت: ${location.time}`}
          />
        ))}
      </MapView>

      {/* Back Button Overlay */}
      <TouchableOpacity
        style={styles.backButtonOverlay}
        onPress={() => router.back()}
        activeOpacity={0.7}
      >
        <Text style={styles.backButtonIcon}>←</Text>
      </TouchableOpacity>

      {/* Trip Info Card Overlay */}
      <View style={styles.tripInfoOverlay}>
        <View style={styles.modernTripCard}>
          <View style={styles.tripHeader}>
            <Text style={styles.tripTitle}>{TRIP_DATA.title}</Text>
            <View style={styles.timeRange}>
              <Text style={styles.timeText}>{TRIP_DATA.startTime} - {TRIP_DATA.endTime}</Text>
            </View>
          </View>

          <View style={styles.tripDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailIcon}>🎓</Text>
              <Text style={styles.detailText}>{TRIP_DATA.destination}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailIcon}>👥</Text>
              <Text style={styles.detailText}>{TRIP_DATA.studentCount} طالب</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Action Buttons Overlay */}
      <View style={styles.actionButtonsOverlay}>
        {!tripStarted ? (
          <>
            <TouchableOpacity
              style={[styles.modernStartButton, isStarting && styles.startButtonDisabled]}
              onPress={handleStartTrip}
              disabled={isStarting}
            >
              <Text style={styles.modernButtonText}>
                {isStarting ? 'جاري البدء...' : 'إبدأ الان'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.modernUnavailableButton} disabled>
              <Text style={styles.modernUnavailableText}>غير متاح</Text>
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity
            style={styles.modernEndButton}
            onPress={handleEndTrip}
          >
            <Text style={styles.modernButtonText}>إنهاء الرحلة</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Navigation Bar */}
      <DriverNavigationBar currentScreen="trip-tracking" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  fullScreenMap: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  backButtonOverlay: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 40,
    left: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  backButtonIcon: {
    fontSize: 20,
    color: '#1F2937',
    fontWeight: 'bold',
  },
  tripInfoOverlay: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 110 : 100,
    left: 20,
    right: 20,
  },
  modernTripCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 10,
    backdropFilter: 'blur(10px)',
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tripTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
  },
  timeRange: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  tripDetails: {
    gap: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  detailIcon: {
    fontSize: 20,
  },
  detailText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    flex: 1,
  },
  actionButtonsOverlay: {
    position: 'absolute',
    bottom: 100, // Above navigation bar
    left: 20,
    right: 20,
    gap: 12,
  },
  modernStartButton: {
    backgroundColor: Colors.light.success,
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 10,
  },
  modernEndButton: {
    backgroundColor: Colors.light.error,
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 10,
  },
  modernUnavailableButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  startButtonDisabled: {
    backgroundColor: 'rgba(156, 163, 175, 0.8)',
    shadowOpacity: 0.1,
  },
  modernButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '700',
  },
  modernUnavailableText: {
    color: '#9CA3AF',
    fontSize: 18,
    fontWeight: '600',
  },
});

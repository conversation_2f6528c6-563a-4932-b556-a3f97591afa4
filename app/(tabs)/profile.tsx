import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  Dimensions,
  Image,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

const { width, height } = Dimensions.get('window');

// Mock user data
const USER_DATA = {
  name: 'أحمد محمد',
  email: '<EMAIL>',
  phone: '+212 6 12 34 56 78',
  university: 'جامعة محمد الخامس',
  city: 'الرباط',
  neighborhood: 'أكدال',
  memberSince: 'سبتمبر 2024',
  totalTrips: 45,
  savedMoney: '1,250 MAD',
  carbonSaved: '125 kg CO2',
};

// Mock trip history
const TRIP_HISTORY = [
  {
    id: 1,
    date: '2024-01-15',
    from: 'منزل الطالب',
    to: 'الجامعة',
    status: 'completed',
    cost: '25 MAD',
  },
  {
    id: 2,
    date: '2024-01-14',
    from: 'منزل الطالب',
    to: 'الجامعة',
    status: 'completed',
    cost: '25 MAD',
  },
  {
    id: 3,
    date: '2024-01-13',
    from: 'منزل الطالب',
    to: 'الجامعة',
    status: 'cancelled',
    cost: '0 MAD',
  },
];

export default function ProfileScreen() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('info');

  const handleEditProfile = () => {
    Alert.alert('تحرير الملف الشخصي', 'سيتم إضافة هذه الميزة قريباً');
  };

  const handleSettings = () => {
    Alert.alert('الإعدادات', 'سيتم إضافة هذه الميزة قريباً');
  };

  const handleLogout = () => {
    Alert.alert(
      'تسجيل الخروج',
      'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        { 
          text: 'تسجيل الخروج', 
          style: 'destructive',
          onPress: () => router.replace('/splash')
        },
      ]
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'info':
        return (
          <View style={styles.tabContent}>
            {/* Personal Information */}
            <View style={styles.infoSection}>
              <Text style={styles.sectionTitle}>المعلومات الشخصية</Text>
              
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>الاسم الكامل</Text>
                <Text style={styles.infoValue}>{USER_DATA.name}</Text>
              </View>
              
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>البريد الإلكتروني</Text>
                <Text style={styles.infoValue}>{USER_DATA.email}</Text>
              </View>
              
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>رقم الهاتف</Text>
                <Text style={styles.infoValue}>{USER_DATA.phone}</Text>
              </View>
              
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>الجامعة</Text>
                <Text style={styles.infoValue}>{USER_DATA.university}</Text>
              </View>
              
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>المدينة</Text>
                <Text style={styles.infoValue}>{USER_DATA.city}</Text>
              </View>
              
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>الحي</Text>
                <Text style={styles.infoValue}>{USER_DATA.neighborhood}</Text>
              </View>
            </View>
          </View>
        );
      
      case 'trips':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.sectionTitle}>سجل الرحلات</Text>
            {TRIP_HISTORY.map((trip) => (
              <View key={trip.id} style={styles.tripItem}>
                <View style={styles.tripHeader}>
                  <Text style={styles.tripDate}>{trip.date}</Text>
                  <View style={[
                    styles.tripStatus,
                    { backgroundColor: trip.status === 'completed' ? Colors.light.success : Colors.light.error }
                  ]}>
                    <Text style={styles.tripStatusText}>
                      {trip.status === 'completed' ? 'مكتملة' : 'ملغية'}
                    </Text>
                  </View>
                </View>
                <Text style={styles.tripRoute}>{trip.from} ← {trip.to}</Text>
                <Text style={styles.tripCost}>{trip.cost}</Text>
              </View>
            ))}
          </View>
        );
      
      case 'stats':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.sectionTitle}>الإحصائيات</Text>
            
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{USER_DATA.totalTrips}</Text>
                <Text style={styles.statLabel}>إجمالي الرحلات</Text>
              </View>
              
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{USER_DATA.savedMoney}</Text>
                <Text style={styles.statLabel}>المال المُوفر</Text>
              </View>
              
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{USER_DATA.carbonSaved}</Text>
                <Text style={styles.statLabel}>الكربون المُوفر</Text>
              </View>
              
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{USER_DATA.memberSince}</Text>
                <Text style={styles.statLabel}>عضو منذ</Text>
              </View>
            </View>
          </View>
        );
      
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.light.gradientStart} />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.profileImageContainer}>
          <View style={styles.profileImage}>
            <Text style={styles.profileInitials}>أم</Text>
          </View>
          <TouchableOpacity style={styles.editImageButton}>
            <Text style={styles.editImageIcon}>📷</Text>
          </TouchableOpacity>
        </View>
        
        <Text style={styles.userName}>{USER_DATA.name}</Text>
        <Text style={styles.userEmail}>{USER_DATA.email}</Text>
        
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleEditProfile}>
            <Text style={styles.actionButtonText}>تحرير الملف</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.actionButton, styles.settingsButton]} onPress={handleSettings}>
            <Text style={styles.settingsButtonText}>الإعدادات</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Tab Navigation */}
      <View style={styles.tabNavigation}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'info' && styles.activeTabButton]}
          onPress={() => setActiveTab('info')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'info' && styles.activeTabButtonText]}>
            المعلومات
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'trips' && styles.activeTabButton]}
          onPress={() => setActiveTab('trips')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'trips' && styles.activeTabButtonText]}>
            الرحلات
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'stats' && styles.activeTabButton]}
          onPress={() => setActiveTab('stats')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'stats' && styles.activeTabButtonText]}>
            الإحصائيات
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderTabContent()}
        
        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>تسجيل الخروج</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.gradientStart,
  },
  header: {
    backgroundColor: Colors.light.gradientEnd,
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 30,
    paddingHorizontal: 24,
    alignItems: 'center',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 4,
    borderColor: Colors.light.gradientStart,
  },
  profileInitials: {
    fontSize: 36,
    fontWeight: '700',
    color: Colors.light.textOnBlue,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.gradientStart,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.light.primary,
  },
  editImageIcon: {
    fontSize: 16,
  },
  userName: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  userEmail: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 20,
    textAlign: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  actionButtonText: {
    color: Colors.light.textOnBlue,
    fontSize: 14,
    fontWeight: '600',
  },
  settingsButton: {
    backgroundColor: Colors.light.gradientStart,
    borderWidth: 1,
    borderColor: Colors.light.borderBlue,
  },
  settingsButtonText: {
    color: Colors.light.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  tabNavigation: {
    flexDirection: 'row',
    backgroundColor: Colors.light.gradientStart,
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 12,
  },
  activeTabButton: {
    backgroundColor: Colors.light.gradientEnd,
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textSecondary,
  },
  activeTabButtonText: {
    color: Colors.light.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  tabContent: {
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 20,
    textAlign: 'right',
  },
  infoSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  infoItem: {
    marginBottom: 16,
  },
  infoLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
    textAlign: 'right',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'right',
  },
  tripItem: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tripDate: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  tripStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  tripStatusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textOnBlue,
  },
  tripRoute: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
    textAlign: 'right',
  },
  tripCost: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    textAlign: 'right',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    padding: 20,
    width: (width - 60) / 2,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  logoutButton: {
    backgroundColor: Colors.light.error,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginVertical: 30,
  },
  logoutButtonText: {
    color: Colors.light.textOnBlue,
    fontSize: 16,
    fontWeight: '600',
  },
});

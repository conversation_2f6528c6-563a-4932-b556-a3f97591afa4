import GoogleMapsHome from '@/components/GoogleMapsHome';
import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

const { width, height } = Dimensions.get('window');

// Static location for demonstration (Rabat, Morocco)
const STATIC_LOCATION = {
  latitude: 34.0209,
  longitude: -6.8416,
  latitudeDelta: 0.01,
  longitudeDelta: 0.01,
};

export default function HomeScreen() {
  const router = useRouter();
  const [location, setLocation] = useState(STATIC_LOCATION);
  const [entryTime, setEntryTime] = useState('');
  const [exitTime, setExitTime] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Set static location immediately
    setLocation(STATIC_LOCATION);
  }, []);

  const handleConfirmBooking = async () => {
    if (!entryTime || !exitTime) {
      Alert.alert('خطأ', 'الرجاء إدخال أوقات الدخول والخروج');
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      // Redirect to trip details page
      router.push('/trip-details');
    }, 2000);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.light.gradientStart} />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>🚌 موبي - نقل الطلاب</Text>
        <Text style={styles.headerSubtitle}>احجز رحلتك اليومية للمدرسة</Text>
      </View>

      {/* Map Container with Google Maps */}
      <View style={styles.mapContainer}>
        <GoogleMapsHome
          location={location}
          students={[{
            id: 1,
            name: 'موقع الطالب',
            university: 'الرباط، المغرب',
            latitude: location.latitude,
            longitude: location.longitude,
          }]}
          style={styles.map}
        />
      </View>

      {/* Input Section */}
      <ScrollView style={styles.inputSection} showsVerticalScrollIndicator={false}>
        {/* School Entry Time Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>🕐 وقت دخول المدرسة</Text>
          <TextInput
            style={styles.timeInput}
            placeholder="مثال: 08:00"
            placeholderTextColor="#9CA3AF"
            value={entryTime}
            onChangeText={setEntryTime}
            textAlign="center"
            keyboardType="default"
          />
          <Text style={styles.inputHint}>أدخل الوقت الذي تدخل فيه المدرسة</Text>
        </View>

        {/* School Exit Time Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>🕐 وقت خروج المدرسة</Text>
          <TextInput
            style={styles.timeInput}
            placeholder="مثال: 15:30"
            placeholderTextColor="#9CA3AF"
            value={exitTime}
            onChangeText={setExitTime}
            textAlign="center"
            keyboardType="default"
          />
          <Text style={styles.inputHint}>أدخل الوقت الذي تخرج فيه من المدرسة</Text>
        </View>



        {/* Confirm Booking Button */}
        <TouchableOpacity
          style={[styles.confirmButton, isLoading && styles.confirmButtonDisabled]}
          onPress={handleConfirmBooking}
          disabled={isLoading}
        >
          <Text style={styles.confirmButtonText}>
            {isLoading ? '⏳ جاري التأكيد...' : '✅ تأكيد الحجز'}
          </Text>
        </TouchableOpacity>

        {/* Info Section */}
        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            💡 سيتم استخدام هذه المعلومات لتنظيم مواعيد الحافلة وتحديد نقاط التوقف
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.gradientStart,
  },
  header: {
    backgroundColor: Colors.light.gradientEnd,
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  mapContainer: {
    height: height * 0.35,
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  map: {
    flex: 1,
  },
  loadingMap: {
    flex: 1,
    backgroundColor: '#F8FAFC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
  inputSection: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
    textAlign: 'right',
  },
  timeInput: {
    backgroundColor: Colors.light.surface,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 18,
    color: Colors.light.text,
    fontWeight: '600',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  inputHint: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginTop: 4,
    fontStyle: 'italic',
  },

  confirmButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 18,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: Colors.light.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  confirmButtonDisabled: {
    backgroundColor: Colors.light.textMuted,
    shadowOpacity: 0.1,
  },
  confirmButtonText: {
    color: Colors.light.textOnBlue,
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoContainer: {
    backgroundColor: Colors.light.gradientEnd,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.borderBlue,
  },
  infoText: {
    fontSize: 14,
    color: Colors.light.textBlue,
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 20,
  },
});

import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  Dimensions,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

const { width, height } = Dimensions.get('window');

// Mock trips data
const UPCOMING_TRIPS = [
  {
    id: 1,
    date: '2024-01-16',
    time: '07:40',
    from: 'منزل الطالب',
    to: 'الجامعة',
    status: 'confirmed',
    driver: 'محمد أحمد',
    busNumber: 'RAB-123',
    estimatedArrival: '08:30',
  },
  {
    id: 2,
    date: '2024-01-17',
    time: '07:40',
    from: 'منزل الطالب',
    to: 'الجامعة',
    status: 'pending',
    driver: 'سعيد علي',
    busNumber: 'RAB-456',
    estimatedArrival: '08:30',
  },
];

const PAST_TRIPS = [
  {
    id: 3,
    date: '2024-01-15',
    time: '07:40',
    from: 'منزل الطالب',
    to: 'الجامعة',
    status: 'completed',
    driver: 'محمد أحمد',
    busNumber: 'RAB-123',
    actualArrival: '08:25',
    cost: '25 MAD',
  },
  {
    id: 4,
    date: '2024-01-14',
    time: '07:40',
    from: 'منزل الطالب',
    to: 'الجامعة',
    status: 'completed',
    driver: 'سعيد علي',
    busNumber: 'RAB-456',
    actualArrival: '08:35',
    cost: '25 MAD',
  },
  {
    id: 5,
    date: '2024-01-13',
    time: '07:40',
    from: 'منزل الطالب',
    to: 'الجامعة',
    status: 'cancelled',
    driver: 'محمد أحمد',
    busNumber: 'RAB-123',
    cost: '0 MAD',
  },
];

export default function TripsScreen() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('upcoming');

  const handleBookNewTrip = () => {
    router.push('/(tabs)');
  };

  const handleViewTripDetails = (tripId: number) => {
    router.push('/trip-details');
  };

  const handleCancelTrip = (tripId: number) => {
    Alert.alert(
      'إلغاء الرحلة',
      'هل أنت متأكد من أنك تريد إلغاء هذه الرحلة؟',
      [
        { text: 'لا', style: 'cancel' },
        { 
          text: 'نعم، إلغاء', 
          style: 'destructive',
          onPress: () => Alert.alert('تم الإلغاء', 'تم إلغاء الرحلة بنجاح')
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return Colors.light.success;
      case 'pending':
        return Colors.light.warning;
      case 'completed':
        return Colors.light.primary;
      case 'cancelled':
        return Colors.light.error;
      default:
        return Colors.light.textMuted;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'مؤكدة';
      case 'pending':
        return 'في الانتظار';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  };

  const renderUpcomingTrips = () => (
    <View style={styles.tripsContainer}>
      {UPCOMING_TRIPS.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateTitle}>لا توجد رحلات قادمة</Text>
          <Text style={styles.emptyStateText}>احجز رحلتك الأولى الآن</Text>
          <TouchableOpacity style={styles.bookButton} onPress={handleBookNewTrip}>
            <Text style={styles.bookButtonText}>احجز رحلة</Text>
          </TouchableOpacity>
        </View>
      ) : (
        UPCOMING_TRIPS.map((trip) => (
          <View key={trip.id} style={styles.tripCard}>
            <View style={styles.tripHeader}>
              <View style={styles.tripDateContainer}>
                <Text style={styles.tripDate}>{trip.date}</Text>
                <Text style={styles.tripTime}>{trip.time}</Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(trip.status) }]}>
                <Text style={styles.statusText}>{getStatusText(trip.status)}</Text>
              </View>
            </View>
            
            <View style={styles.tripRoute}>
              <Text style={styles.routeText}>{trip.from}</Text>
              <Text style={styles.routeArrow}>←</Text>
              <Text style={styles.routeText}>{trip.to}</Text>
            </View>
            
            <View style={styles.tripDetails}>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>السائق:</Text>
                <Text style={styles.detailValue}>{trip.driver}</Text>
              </View>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>رقم الحافلة:</Text>
                <Text style={styles.detailValue}>{trip.busNumber}</Text>
              </View>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>الوصول المتوقع:</Text>
                <Text style={styles.detailValue}>{trip.estimatedArrival}</Text>
              </View>
            </View>
            
            <View style={styles.tripActions}>
              <TouchableOpacity 
                style={styles.actionButton} 
                onPress={() => handleViewTripDetails(trip.id)}
              >
                <Text style={styles.actionButtonText}>عرض التفاصيل</Text>
              </TouchableOpacity>
              
              {trip.status === 'pending' && (
                <TouchableOpacity 
                  style={[styles.actionButton, styles.cancelButton]} 
                  onPress={() => handleCancelTrip(trip.id)}
                >
                  <Text style={styles.cancelButtonText}>إلغاء</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        ))
      )}
    </View>
  );

  const renderPastTrips = () => (
    <View style={styles.tripsContainer}>
      {PAST_TRIPS.map((trip) => (
        <View key={trip.id} style={styles.tripCard}>
          <View style={styles.tripHeader}>
            <View style={styles.tripDateContainer}>
              <Text style={styles.tripDate}>{trip.date}</Text>
              <Text style={styles.tripTime}>{trip.time}</Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(trip.status) }]}>
              <Text style={styles.statusText}>{getStatusText(trip.status)}</Text>
            </View>
          </View>
          
          <View style={styles.tripRoute}>
            <Text style={styles.routeText}>{trip.from}</Text>
            <Text style={styles.routeArrow}>←</Text>
            <Text style={styles.routeText}>{trip.to}</Text>
          </View>
          
          <View style={styles.tripDetails}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>السائق:</Text>
              <Text style={styles.detailValue}>{trip.driver}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>رقم الحافلة:</Text>
              <Text style={styles.detailValue}>{trip.busNumber}</Text>
            </View>
            {trip.status === 'completed' && (
              <>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>الوصول الفعلي:</Text>
                  <Text style={styles.detailValue}>{trip.actualArrival}</Text>
                </View>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>التكلفة:</Text>
                  <Text style={[styles.detailValue, styles.costText]}>{trip.cost}</Text>
                </View>
              </>
            )}
          </View>
        </View>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.light.gradientStart} />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>رحلاتي</Text>
        <TouchableOpacity style={styles.newTripButton} onPress={handleBookNewTrip}>
          <Text style={styles.newTripButtonText}>+ رحلة جديدة</Text>
        </TouchableOpacity>
      </View>
      
      {/* Tab Navigation */}
      <View style={styles.tabNavigation}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'upcoming' && styles.activeTabButton]}
          onPress={() => setActiveTab('upcoming')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'upcoming' && styles.activeTabButtonText]}>
            القادمة ({UPCOMING_TRIPS.length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'past' && styles.activeTabButton]}
          onPress={() => setActiveTab('past')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'past' && styles.activeTabButtonText]}>
            السابقة ({PAST_TRIPS.length})
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'upcoming' ? renderUpcomingTrips() : renderPastTrips()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.gradientStart,
  },
  header: {
    backgroundColor: Colors.light.gradientEnd,
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 20,
    paddingHorizontal: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.light.text,
  },
  newTripButton: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  newTripButtonText: {
    color: Colors.light.textOnBlue,
    fontSize: 14,
    fontWeight: '600',
  },
  tabNavigation: {
    flexDirection: 'row',
    backgroundColor: Colors.light.gradientStart,
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 12,
  },
  activeTabButton: {
    backgroundColor: Colors.light.gradientEnd,
  },
  tabButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textSecondary,
  },
  activeTabButtonText: {
    color: Colors.light.primary,
  },
  content: {
    flex: 1,
  },
  tripsContainer: {
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 24,
    textAlign: 'center',
  },
  bookButton: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 24,
  },
  bookButtonText: {
    color: Colors.light.textOnBlue,
    fontSize: 16,
    fontWeight: '600',
  },
  tripCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tripDateContainer: {
    flex: 1,
  },
  tripDate: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  tripTime: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textOnBlue,
  },
  tripRoute: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  routeText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    flex: 1,
    textAlign: 'center',
  },
  routeArrow: {
    fontSize: 18,
    color: Colors.light.primary,
    marginHorizontal: 8,
  },
  tripDetails: {
    marginBottom: 16,
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  costText: {
    color: Colors.light.primary,
  },
  tripActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: Colors.light.primary,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  actionButtonText: {
    color: Colors.light.textOnBlue,
    fontSize: 14,
    fontWeight: '600',
  },
  cancelButton: {
    backgroundColor: Colors.light.error,
  },
  cancelButtonText: {
    color: Colors.light.textOnBlue,
    fontSize: 14,
    fontWeight: '600',
  },
});

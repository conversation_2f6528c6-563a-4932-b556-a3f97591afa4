import GoogleMapsRoute from '@/components/GoogleMapsRoute';
import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Dimensions,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

const { width, height } = Dimensions.get('window');

// Static locations for the trip
const TRIP_LOCATIONS = [
  {
    id: 1,
    name: 'منزل الطالب',
    type: 'home',
    latitude: 34.0209,
    longitude: -6.8416,
  },
  {
    id: 2,
    name: 'محطة الحافلة',
    type: 'bus_stop',
    latitude: 34.0250,
    longitude: -6.8350,
  },
  {
    id: 3,
    name: 'الجامعة',
    type: 'university',
    latitude: 34.0300,
    longitude: -6.8200,
  },
];

const TRIP_SCHEDULE = {
  leaveHome: '07:40',
  pickupTime: '07:50',
  arrivalTime: '08:30',
};

// Driver and trip data
const TRIP_DATA = {
  destination: 'جامعة محمد الخامس',
  estimatedTime: '40 دقيقة',
  driver: {
    name: 'أحمد محمد',
    rating: 4.8,
    phoneNumber: '+212600123456',
  },
  vehicle: {
    make: 'Toyota',
    model: 'Hiace',
    year: 2020,
    color: 'أبيض',
    licensePlate: 'A-12345-ب',
  },
};



export default function TripDetailsScreen() {
  const router = useRouter();
  const [isStarting, setIsStarting] = useState(false);

  const handleGoPress = () => {
    setIsStarting(true);

    setTimeout(() => {
      setIsStarting(false);
      Alert.alert(
        'بدء الرحلة',
        'تم إشعار السائق وسيتم تتبع موقعك أثناء الرحلة.',
        [
          {
            text: 'موافق',
            onPress: () => router.push('/live-trip'),
          }
        ]
      );
    }, 2000);
  };

  const mapRegion = {
    latitude: 34.0255,
    longitude: -6.8300,
    latitudeDelta: 0.02,
    longitudeDelta: 0.02,
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Full Screen Map */}
      <View style={styles.mapContainer}>
        <GoogleMapsRoute
          locations={TRIP_LOCATIONS}
          style={styles.fullScreenMap}
          routeData={{
            duration: '40 min',
            distance: '12.5 km',
            eta: TRIP_SCHEDULE.arrivalTime,
          }}
        />
      </View>

      {/* Floating Back Button */}
      <TouchableOpacity
        style={styles.floatingBackButton}
        onPress={() => router.back()}
        activeOpacity={0.8}
      >
        <Text style={styles.backIcon}>←</Text>
      </TouchableOpacity>

      {/* Trip Details Overlay */}
      <View style={styles.tripDetailsOverlay}>
        <View style={styles.tripCard}>
          {/* Destination Info */}
          <View style={styles.destinationSection}>
            <Text style={styles.destinationTitle}>{TRIP_DATA.destination}</Text>
            <Text style={styles.estimatedTime}>الوصول المتوقع: {TRIP_SCHEDULE.arrivalTime}</Text>
          </View>

          {/* Driver Info */}
          <View style={styles.driverSection}>
            <View style={styles.driverInfo}>
              <View style={styles.driverAvatar}>
                <Text style={styles.driverInitial}>{TRIP_DATA.driver.name.charAt(0)}</Text>
              </View>
              <View style={styles.driverDetails}>
                <Text style={styles.driverName}>{TRIP_DATA.driver.name}</Text>
                <View style={styles.ratingContainer}>
                  <Text style={styles.ratingStars}>⭐</Text>
                  <Text style={styles.ratingText}>{TRIP_DATA.driver.rating}</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Vehicle Info */}
          <View style={styles.vehicleSection}>
            <View style={styles.vehicleInfo}>
              <Text style={styles.vehicleText}>
                {TRIP_DATA.vehicle.color} {TRIP_DATA.vehicle.make} {TRIP_DATA.vehicle.model}
              </Text>
              <Text style={styles.licensePlate}>{TRIP_DATA.vehicle.licensePlate}</Text>
            </View>
          </View>

          {/* Action Button */}
          <TouchableOpacity
            style={[styles.startTripButton, isStarting && styles.startTripButtonDisabled]}
            onPress={handleGoPress}
            disabled={isStarting}
            activeOpacity={0.8}
          >
            <Text style={styles.startTripButtonText}>
              {isStarting ? 'جاري البدء...' : 'بدء الرحلة'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  // Main Container
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },

  // Map Container
  mapContainer: {
    flex: 1,
  },
  fullScreenMap: {
    flex: 1,
  },

  // Floating Back Button
  floatingBackButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 40,
    left: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  backIcon: {
    fontSize: 20,
    color: Colors.light.primary,
    fontWeight: '600',
  },

  // Trip Details Overlay
  tripDetailsOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },

  // Trip Card
  tripCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderRadius: 24,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 12,
    backdropFilter: 'blur(10px)',
  },

  // Destination Section
  destinationSection: {
    marginBottom: 20,
    alignItems: 'center',
  },
  destinationTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  estimatedTime: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },

  // Driver Section
  driverSection: {
    marginBottom: 20,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  driverInitial: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
    textAlign: 'right',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  ratingStars: {
    fontSize: 16,
    marginRight: 6,
  },
  ratingText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },

  // Vehicle Section
  vehicleSection: {
    marginBottom: 24,
  },
  vehicleInfo: {
    backgroundColor: '#f8fafc',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
  },
  vehicleText: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  licensePlate: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '700',
    backgroundColor: Colors.light.primary + '15',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },

  // Start Trip Button
  startTripButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  startTripButtonDisabled: {
    backgroundColor: Colors.light.textTertiary,
    shadowOpacity: 0.1,
  },
  startTripButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
  },
});
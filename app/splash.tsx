import React, { useEffect } from 'react';
import {
    Dimensions,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withDelay,
    withSequence,
    withSpring,
    withTiming
} from 'react-native-reanimated';

import { router } from 'expo-router';

const { width, height } = Dimensions.get('window');

const SplashScreen = () => {
  // Animation values
  const titleTranslateY = useSharedValue(50);
  const titleOpacity = useSharedValue(0);
  const buttonsTranslateY = useSharedValue(100);
  const buttonsOpacity = useSharedValue(0);
  const backgroundScale = useSharedValue(0.8);
  const loginButtonScale = useSharedValue(0.9);
  const registerButtonScale = useSharedValue(0.9);

  useEffect(() => {
    // Start the animation sequence
    startAnimationSequence();
  }, []);

  const startAnimationSequence = () => {
    // Background animation
    backgroundScale.value = withSpring(1, {
      damping: 15,
      stiffness: 100,
    });

    // Title animation - slide up and fade in
    titleTranslateY.value = withDelay(
      300,
      withSpring(0, {
        damping: 15,
        stiffness: 100,
      })
    );
    titleOpacity.value = withDelay(300, withTiming(1, { duration: 600 }));

    // Buttons animation - slide up and fade in
    buttonsTranslateY.value = withDelay(
      600,
      withSpring(0, {
        damping: 15,
        stiffness: 100,
      })
    );
    buttonsOpacity.value = withDelay(600, withTiming(1, { duration: 600 }));

    // Button scale animations
    loginButtonScale.value = withDelay(
      900,
      withSpring(1, {
        damping: 10,
        stiffness: 100,
      })
    );
    registerButtonScale.value = withDelay(
      1100,
      withSpring(1, {
        damping: 10,
        stiffness: 100,
      })
    );
  };

  // Animated styles
  const backgroundAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: backgroundScale.value }],
    };
  });

  const titleAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: titleTranslateY.value }],
      opacity: titleOpacity.value,
    };
  });

  const buttonsAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: buttonsTranslateY.value }],
      opacity: buttonsOpacity.value,
    };
  });

  const loginButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: loginButtonScale.value }],
    };
  });

  const registerButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: registerButtonScale.value }],
    };
  });

  const handleLoginPress = () => {
    loginButtonScale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    // Navigate to login screen (you can implement this later)
    setTimeout(() => {
      router.push('/login');
    }, 200);
  };

  const handleDriverLoginPress = () => {
    registerButtonScale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    // Navigate to driver login screen
    setTimeout(() => {
      router.push('/driver-login');
    }, 200);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E3A8A" />
      
      {/* Animated Background */}
      <Animated.View style={[styles.backgroundContainer, backgroundAnimatedStyle]}>
        <View style={styles.gradient} />
      </Animated.View>

      {/* Decorative circles */}
      <View style={styles.circle1} />
      <View style={styles.circle2} />
      <View style={styles.circle3} />

      {/* Main content */}
      <View style={styles.content}>
        {/* App Title */}
        <Animated.View style={[styles.titleContainer, titleAnimatedStyle]}>
          <Text style={styles.title}>Mobi</Text>
          <Text style={styles.subtitle}>رفيقك المحمول</Text>
        </Animated.View>

        {/* Buttons */}
        <Animated.View style={[styles.buttonsContainer, buttonsAnimatedStyle]}>
          <Animated.View style={loginButtonAnimatedStyle}>
            <TouchableOpacity style={styles.loginButton} onPress={handleLoginPress}>
              <Text style={styles.loginButtonText}>تسجيل الدخول</Text>
            </TouchableOpacity>
          </Animated.View>

          <Animated.View style={registerButtonAnimatedStyle}>
            <TouchableOpacity style={styles.registerButton} onPress={handleDriverLoginPress}>
              <Text style={styles.registerButtonText}>Drive login</Text>
            </TouchableOpacity>
          </Animated.View>

          <TouchableOpacity
            style={styles.skipButton}
            onPress={() => router.push('/(tabs)')}
          >
            <Text style={styles.skipButtonText}>تخطي الآن</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1E3A8A',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gradient: {
    flex: 1,
    backgroundColor: '#3B82F6',
  },
  circle1: {
    position: 'absolute',
    top: -50,
    right: -50,
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  circle2: {
    position: 'absolute',
    top: height * 0.3,
    left: -80,
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  circle3: {
    position: 'absolute',
    bottom: -100,
    right: -100,
    width: 300,
    height: 300,
    borderRadius: 150,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 80,
  },
  title: {
    fontSize: 72,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    fontWeight: '300',
    writingDirection: 'rtl',
  },
  buttonsContainer: {
    width: '100%',
    gap: 16,
  },
  loginButton: {
    backgroundColor: 'white',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 25,
    alignItems: 'center',
    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.3)',
    elevation: 10,
  },
  loginButtonText: {
    color: '#1E3A8A',
    fontSize: 18,
    fontWeight: '600',
    writingDirection: 'rtl',
  },
  registerButton: {
    backgroundColor: 'transparent',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 25,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  registerButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    writingDirection: 'rtl',
  },
  skipButton: {
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  skipButtonText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
    fontWeight: '400',
    writingDirection: 'rtl',
  },
});

export default SplashScreen;

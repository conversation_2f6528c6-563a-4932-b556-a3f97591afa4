import React from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';

interface Student {
  id: number;
  name: string;
  university: string;
  latitude: number;
  longitude: number;
}

interface MapComponentProps {
  location: any;
  students: Student[];
  style: any;
}

// Web map component - shows student list instead of map
const MapComponent: React.FC<MapComponentProps> = ({ students, style }) => {
  return (
    <View style={[style, styles.webMapPlaceholder]}>
      <Text style={styles.webMapTitle}>🗺️ خريطة الطلاب</Text>
      <Text style={styles.webMapSubtitle}>المواقع المتاحة:</Text>
      <ScrollView style={styles.studentList} showsVerticalScrollIndicator={false}>
        {students.map((student) => (
          <View key={student.id} style={styles.studentItem}>
            <Text style={styles.studentName}>👤 {student.name}</Text>
            <Text style={styles.studentUniversity}>🎓 {student.university}</Text>
            <Text style={styles.studentLocation}>
              📍 {student.latitude.toFixed(4)}, {student.longitude.toFixed(4)}
            </Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  webMapPlaceholder: {
    backgroundColor: '#F8FAFC',
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  webMapTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  webMapSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 16,
    textAlign: 'center',
  },
  studentList: {
    width: '100%',
    maxHeight: 200,
  },
  studentItem: {
    backgroundColor: 'white',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  studentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
    textAlign: 'right',
  },
  studentUniversity: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
    textAlign: 'right',
  },
  studentLocation: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'right',
  },
});

export default MapComponent;

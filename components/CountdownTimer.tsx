import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Colors } from '@/constants/Colors';

interface CountdownTimerProps {
  initialSeconds: number;
  onComplete?: () => void;
  style?: any;
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
  label?: string;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({
  initialSeconds,
  onComplete,
  style,
  size = 'medium',
  showLabel = true,
  label = 'الوقت المتبقي'
}) => {
  const [seconds, setSeconds] = useState(initialSeconds);

  useEffect(() => {
    if (seconds <= 0) {
      onComplete?.();
      return;
    }

    const timer = setInterval(() => {
      setSeconds((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          onComplete?.();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [seconds, onComplete]);

  const formatTime = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const mins = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          fontSize: 16,
          labelSize: 12,
          padding: 8,
        };
      case 'large':
        return {
          fontSize: 32,
          labelSize: 16,
          padding: 20,
        };
      default: // medium
        return {
          fontSize: 24,
          labelSize: 14,
          padding: 16,
        };
    }
  };

  const sizeStyles = getSizeStyles();

  const getTimeColor = () => {
    if (seconds <= 300) return Colors.light.error; // Red for last 5 minutes
    if (seconds <= 600) return Colors.light.warning; // Orange for last 10 minutes
    return Colors.light.primary; // Blue for normal time
  };

  return (
    <View style={[styles.container, style, { padding: sizeStyles.padding }]}>
      {showLabel && (
        <Text style={[styles.label, { fontSize: sizeStyles.labelSize }]}>
          {label}
        </Text>
      )}
      <Text style={[
        styles.timer,
        {
          fontSize: sizeStyles.fontSize,
          color: getTimeColor(),
        }
      ]}>
        {formatTime(seconds)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  label: {
    color: Colors.light.textSecondary,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  timer: {
    fontWeight: '700',
    textAlign: 'center',
    fontVariant: ['tabular-nums'],
  },
});

export default CountdownTimer;

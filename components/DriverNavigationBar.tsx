import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useRouter } from 'expo-router';
import { Colors } from '@/constants/Colors';

interface DriverNavigationBarProps {
  currentScreen?: 'trip-tracking' | 'live-trip' | 'vehicle-info' | 'profile';
}

const DriverNavigationBar: React.FC<DriverNavigationBarProps> = ({ currentScreen }) => {
  const router = useRouter();

  const handleHomePress = () => {
    router.push('/splash');
  };

  const handleTripInfoPress = () => {
    router.push('/driver-trip-tracking');
  };

  const handleProfilePress = () => {
    router.push('/driver-profile');
  };

  const isActive = (screen: string) => currentScreen === screen;

  return (
    <View style={styles.container}>
      <View style={styles.navigationBar}>
        {/* Home/Menu Button */}
        <TouchableOpacity
          style={[styles.navButton, isActive('home') && styles.activeNavButton]}
          onPress={handleHomePress}
          activeOpacity={0.7}
        >
          <View style={styles.navButtonContent}>
            <Text style={[styles.navButtonIcon, isActive('home') && styles.activeNavButtonText]}>
              m
            </Text>
            <Text style={[styles.navButtonLabel, isActive('home') && styles.activeNavButtonText]}>
              القائمة
            </Text>
          </View>
        </TouchableOpacity>

        {/* Trip Info Button */}
        <TouchableOpacity
          style={[styles.navButton, isActive('trip-tracking') && styles.activeNavButton]}
          onPress={handleTripInfoPress}
          activeOpacity={0.7}
        >
          <View style={styles.navButtonContent}>
            <Text style={[styles.navButtonIcon, isActive('trip-tracking') && styles.activeNavButtonText]}>
              🚌
            </Text>
            <Text style={[styles.navButtonLabel, isActive('trip-tracking') && styles.activeNavButtonText]}>
              معلومات الرحلة
            </Text>
          </View>
        </TouchableOpacity>

        {/* Profile Button */}
        <TouchableOpacity
          style={[styles.navButton, isActive('profile') && styles.activeNavButton]}
          onPress={handleProfilePress}
          activeOpacity={0.7}
        >
          <View style={styles.navButtonContent}>
            <Text style={[styles.navButtonIcon, isActive('profile') && styles.activeNavButtonText]}>
              👤
            </Text>
            <Text style={[styles.navButtonLabel, isActive('profile') && styles.activeNavButtonText]}>
              الملف الشخصي
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  navigationBar: {
    flexDirection: 'row',
    paddingVertical: 8,
    paddingHorizontal: 16,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  navButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 12,
    marginHorizontal: 4,
  },
  activeNavButton: {
    backgroundColor: Colors.light.primary,
  },
  navButtonContent: {
    alignItems: 'center',
  },
  navButtonIcon: {
    fontSize: 20,
    marginBottom: 4,
    color: '#6B7280',
    fontWeight: '600',
  },
  navButtonLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
    textAlign: 'center',
  },
  activeNavButtonText: {
    color: '#FFFFFF',
  },
});

export default DriverNavigationBar;

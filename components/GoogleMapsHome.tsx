import React, { useEffect, useRef, useState } from 'react';
import { Dimensions, Platform, StyleSheet, Text, View } from 'react-native';
import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { Colors } from '@/constants/Colors';

const { width, height } = Dimensions.get('window');

interface Student {
  id: number;
  name: string;
  university: string;
  latitude: number;
  longitude: number;
}

interface GoogleMapsHomeProps {
  location: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  students: Student[];
  style?: any;
}

const GoogleMapsHome: React.FC<GoogleMapsHomeProps> = ({ 
  location, 
  students, 
  style 
}) => {
  const mapRef = useRef<MapView>(null);
  const [mapReady, setMapReady] = useState(false);

  // Custom map style for a clean, modern look
  const customMapStyle = [
    {
      featureType: 'poi',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'transit',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'road',
      elementType: 'labels.icon',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'water',
      elementType: 'geometry',
      stylers: [{ color: Colors.light.primaryShade }],
    },
    {
      featureType: 'landscape',
      elementType: 'geometry',
      stylers: [{ color: Colors.light.gradientEnd }],
    },
  ];

  useEffect(() => {
    if (mapReady && mapRef.current && students.length > 0) {
      // Fit map to show all student markers with padding
      const coordinates = students.map(student => ({
        latitude: student.latitude,
        longitude: student.longitude,
      }));
      
      mapRef.current.fitToCoordinates(coordinates, {
        edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        animated: true,
      });
    }
  }, [mapReady, students]);

  if (!location) {
    return (
      <View style={[style, styles.errorContainer]}>
        <Text style={styles.errorText}>Loading map...</Text>
      </View>
    );
  }

  return (
    <View style={[style, styles.container]}>
      {/* Map Header */}
      <View style={styles.mapHeader}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>🗺️ خريطة الطلاب</Text>
          <Text style={styles.headerSubtitle}>المواقع المتاحة: {students.length}</Text>
        </View>
        <View style={styles.locationIndicator}>
          <View style={styles.locationDot} />
          <Text style={styles.locationText}>الرباط، المغرب</Text>
        </View>
      </View>

      {/* Google Maps */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={location}
        onMapReady={() => setMapReady(true)}
        showsUserLocation={true}
        showsMyLocationButton={true}
        showsCompass={false}
        showsScale={false}
        mapType="standard"
        customMapStyle={customMapStyle}
      >
        {/* Student Location Markers */}
        {students.map((student) => (
          <Marker
            key={student.id}
            coordinate={{
              latitude: student.latitude,
              longitude: student.longitude,
            }}
            title={student.name}
            description={student.university}
            pinColor={Colors.light.primary}
          />
        ))}
      </MapView>

      {/* Map Footer with Student Info */}
      <View style={styles.mapFooter}>
        <View style={styles.studentInfo}>
          <Text style={styles.studentCount}>
            {students.length} طالب متاح للنقل المشترك
          </Text>
          <Text style={styles.studentSubtext}>
            اختر موقعك وأوقات الدراسة لإيجاد أفضل رحلة
          </Text>
        </View>
        
        <View style={styles.mapLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: Colors.light.primary }]} />
            <Text style={styles.legendText}>مواقع الطلاب</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: Colors.light.secondary }]} />
            <Text style={styles.legendText}>موقعك الحالي</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  mapHeader: {
    backgroundColor: Colors.light.gradientEnd,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  locationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.success,
    marginRight: 8,
  },
  locationText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  map: {
    height: 280,
    width: '100%',
  },
  mapFooter: {
    backgroundColor: Colors.light.backgroundSecondary,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  studentInfo: {
    marginBottom: 16,
  },
  studentCount: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  studentSubtext: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  mapLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 24,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: Colors.light.textMuted,
    fontWeight: '500',
  },
  errorContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 16,
  },
  errorText: {
    fontSize: 16,
    color: Colors.light.textMuted,
  },
});

export default GoogleMapsHome;

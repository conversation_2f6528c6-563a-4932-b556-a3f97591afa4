import React from 'react';
import { Alert, Linking, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Colors } from '@/constants/Colors';

interface DriverInfo {
  id: string;
  name: string;
  photo?: string;
  rating: number;
  totalTrips: number;
  phoneNumber: string;
  vehicle: {
    make: string;
    model: string;
    year: number;
    color: string;
    licensePlate: string;
  };
}

interface DriverInfoPanelProps {
  driver: DriverInfo;
  style?: any;
}

const DriverInfoPanel: React.FC<DriverInfoPanelProps> = ({ driver, style }) => {
  const handleCall = () => {
    Alert.alert(
      'اتصال بالسائق',
      `هل تريد الاتصال بـ ${driver.name}؟`,
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'اتصال',
          onPress: () => {
            Linking.openURL(`tel:${driver.phoneNumber}`);
          },
        },
      ]
    );
  };

  const handleMessage = () => {
    Alert.alert(
      'رسالة للسائق',
      `هل تريد إرسال رسالة لـ ${driver.name}؟`,
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'رسالة',
          onPress: () => {
            Linking.openURL(`sms:${driver.phoneNumber}`);
          },
        },
      ]
    );
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push('⭐');
    }
    if (hasHalfStar) {
      stars.push('⭐');
    }
    while (stars.length < 5) {
      stars.push('☆');
    }
    
    return stars.join('');
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.driverCard}>
        <View style={styles.driverHeader}>
          <Text style={styles.sectionTitle}>معلومات السائق</Text>
        </View>
        
        <View style={styles.driverContent}>
          {/* Driver Photo and Basic Info */}
          <View style={styles.driverMainInfo}>
            <View style={styles.driverPhotoContainer}>
              <View style={styles.driverPhoto}>
                <Text style={styles.driverPhotoText}>
                  {driver.name.charAt(0).toUpperCase()}
                </Text>
              </View>
              <View style={styles.onlineIndicator} />
            </View>
            
            <View style={styles.driverDetails}>
              <Text style={styles.driverName}>{driver.name}</Text>
              <View style={styles.ratingContainer}>
                <Text style={styles.ratingStars}>{renderStars(driver.rating)}</Text>
                <Text style={styles.ratingText}>
                  {driver.rating.toFixed(1)} ({driver.totalTrips} رحلة)
                </Text>
              </View>
            </View>
          </View>
          
          {/* Vehicle Information */}
          <View style={styles.vehicleInfo}>
            <View style={styles.vehicleHeader}>
              <Text style={styles.vehicleTitle}>🚗 معلومات المركبة</Text>
            </View>
            <View style={styles.vehicleDetails}>
              <View style={styles.vehicleRow}>
                <Text style={styles.vehicleLabel}>النوع:</Text>
                <Text style={styles.vehicleValue}>
                  {driver.vehicle.year} {driver.vehicle.make} {driver.vehicle.model}
                </Text>
              </View>
              <View style={styles.vehicleRow}>
                <Text style={styles.vehicleLabel}>اللون:</Text>
                <Text style={styles.vehicleValue}>{driver.vehicle.color}</Text>
              </View>
              <View style={styles.vehicleRow}>
                <Text style={styles.vehicleLabel}>رقم اللوحة:</Text>
                <Text style={styles.licensePlate}>{driver.vehicle.licensePlate}</Text>
              </View>
            </View>
          </View>
          
          {/* Contact Actions */}
          <View style={styles.contactActions}>
            <TouchableOpacity style={styles.callButton} onPress={handleCall}>
              <Text style={styles.contactIcon}>📞</Text>
              <Text style={styles.contactButtonText}>اتصال</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.messageButton} onPress={handleMessage}>
              <Text style={styles.contactIcon}>💬</Text>
              <Text style={styles.contactButtonText}>رسالة</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  driverCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 24,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: Colors.light.borderLight,
  },
  driverHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    textAlign: 'center',
  },
  driverContent: {
    gap: 20,
  },
  driverMainInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverPhotoContainer: {
    position: 'relative',
    marginRight: 16,
  },
  driverPhoto: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  driverPhotoText: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.background,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: Colors.light.success,
    borderWidth: 2,
    borderColor: Colors.light.background,
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 4,
    textAlign: 'right',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  ratingStars: {
    fontSize: 16,
    marginRight: 8,
  },
  ratingText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '600',
  },
  vehicleInfo: {
    backgroundColor: Colors.light.surfaceSecondary,
    borderRadius: 16,
    padding: 16,
  },
  vehicleHeader: {
    marginBottom: 12,
  },
  vehicleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'right',
  },
  vehicleDetails: {
    gap: 8,
  },
  vehicleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  vehicleLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '600',
  },
  vehicleValue: {
    fontSize: 14,
    color: Colors.light.text,
    fontWeight: '600',
    textAlign: 'right',
  },
  licensePlate: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '700',
    backgroundColor: Colors.light.primary + '15',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  contactActions: {
    flexDirection: 'row',
    gap: 12,
  },
  callButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.success,
    paddingVertical: 12,
    borderRadius: 12,
  },
  messageButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.info,
    paddingVertical: 12,
    borderRadius: 12,
  },
  contactIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.background,
  },
});

export default DriverInfoPanel;

import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Colors } from '@/constants/Colors';

interface TripTimerProps {
  startTime: Date;
  estimatedDuration: number; // in minutes
  style?: any;
}

interface TimeData {
  elapsed: string;
  eta: string;
  remaining: string;
}

const TripTimer: React.FC<TripTimerProps> = ({ startTime, estimatedDuration, style }) => {
  const [timeData, setTimeData] = useState<TimeData>({
    elapsed: '00:00',
    eta: '--:--',
    remaining: '--:--'
  });

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}`;
    }
    return `${mins}:${Math.floor((minutes % 1) * 60).toString().padStart(2, '0')}`;
  };

  const formatTimeHHMM = (date: Date): string => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  useEffect(() => {
    const updateTimer = () => {
      const now = new Date();
      const elapsedMs = now.getTime() - startTime.getTime();
      const elapsedMinutes = elapsedMs / (1000 * 60);
      
      // Calculate ETA
      const etaDate = new Date(startTime.getTime() + (estimatedDuration * 60 * 1000));
      
      // Calculate remaining time
      const remainingMinutes = Math.max(0, estimatedDuration - elapsedMinutes);
      
      setTimeData({
        elapsed: formatTime(elapsedMinutes),
        eta: formatTimeHHMM(etaDate),
        remaining: formatTime(remainingMinutes)
      });
    };

    // Update immediately
    updateTimer();
    
    // Update every second
    const interval = setInterval(updateTimer, 1000);
    
    return () => clearInterval(interval);
  }, [startTime, estimatedDuration]);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.timerCard}>
        <View style={styles.timerHeader}>
          <Text style={styles.timerTitle}>⏱️ مؤقت الرحلة</Text>
        </View>
        
        <View style={styles.timerGrid}>
          {/* Elapsed Time */}
          <View style={styles.timerItem}>
            <View style={styles.timerIconContainer}>
              <Text style={styles.timerIcon}>🕐</Text>
            </View>
            <Text style={styles.timerValue}>{timeData.elapsed}</Text>
            <Text style={styles.timerLabel}>الوقت المنقضي</Text>
          </View>
          
          {/* ETA */}
          <View style={styles.timerItem}>
            <View style={styles.timerIconContainer}>
              <Text style={styles.timerIcon}>🎯</Text>
            </View>
            <Text style={styles.timerValue}>{timeData.eta}</Text>
            <Text style={styles.timerLabel}>وقت الوصول المتوقع</Text>
          </View>
          
          {/* Remaining Time */}
          <View style={styles.timerItem}>
            <View style={styles.timerIconContainer}>
              <Text style={styles.timerIcon}>⏳</Text>
            </View>
            <Text style={styles.timerValue}>{timeData.remaining}</Text>
            <Text style={styles.timerLabel}>الوقت المتبقي</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  timerCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 24,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: Colors.light.borderLight,
  },
  timerHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  timerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    textAlign: 'center',
  },
  timerGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  timerItem: {
    alignItems: 'center',
    flex: 1,
  },
  timerIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.light.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  timerIcon: {
    fontSize: 24,
  },
  timerValue: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  timerLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default TripTimer;

import React from 'react';
import { StyleSheet } from 'react-native';
import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';

interface Student {
  id: number;
  name: string;
  university: string;
  latitude: number;
  longitude: number;
}

interface MapComponentProps {
  location: any;
  students: Student[];
  style: any;
}

// Native map component using react-native-maps
const MapComponent: React.FC<MapComponentProps> = ({ location, students, style }) => {
  return (
    <MapView
      style={style}
      provider={PROVIDER_GOOGLE}
      initialRegion={location}
      showsUserLocation={true}
      showsMyLocationButton={true}
    >
      {students.map((student) => (
        <Marker
          key={student.id}
          coordinate={{
            latitude: student.latitude,
            longitude: student.longitude,
          }}
          title={student.name}
          description={student.university}
          pinColor="#3B82F6"
        />
      ))}
    </MapView>
  );
};

export default MapComponent;
